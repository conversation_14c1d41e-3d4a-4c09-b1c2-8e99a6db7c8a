#!/usr/bin/env python3
"""
FastAPI Backend for Psychometrist Portal
Provides REST APIs for all AI-powered psychological assessment features
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import json
import shutil
from pathlib import Path
from datetime import datetime
import uuid
import logging

# Import our AI systems
from document_reader import read_document
from ai_document_analyzer import analyze_document
from ai_report_generator import generate_psychological_report
from s3_manager import s3_manager


# Set up logging
logging.basicConfig(
    filename="app.log",
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Initialize FastAPI app
app = FastAPI(
    title="Deanna's Psychometrist Portal API",
    description="AI-powered psychological assessment and report generation system",
    version="1.0.0"
)

# Add CORS middleware for web frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories for uploads and outputs
UPLOAD_DIR = Path("uploads")
REPORTS_DIR = Path("reports")
TEMP_DIR = Path("temp")

for directory in [UPLOAD_DIR, REPORTS_DIR, TEMP_DIR]:
    directory.mkdir(exist_ok=True)

# Pydantic models for request/response
class DocumentAnalysisResponse(BaseModel):
    document_type: str
    patient_info: Dict[str, Any]
    assessment_details: Dict[str, Any]
    key_scores: List[str]
    main_findings: List[str]
    recommendations: List[str]
    confidence_level: str

# API Endpoints

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a document file to S3 for analysis"""
    try:
        # Read file content
        file_content = await file.read()

        # Upload to S3
        upload_result = s3_manager.upload_file(
            file_content=file_content,
            filename=file.filename,
            folder='uploads',
            metadata={
                'content-type': file.content_type or 'application/octet-stream',
                'uploaded-by': 'api-user'  # In production, get from auth
            }
        )

        return {
            "file_id": upload_result["file_id"],
            "filename": upload_result["filename"],
            "s3_key": upload_result["s3_key"],
            "s3_url": upload_result["s3_url"],
            "size": upload_result["size"],
            "upload_time": upload_result["upload_time"],
            "status": "uploaded_to_s3"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 upload failed: {str(e)}")

@app.post("/analyze-document")
async def analyze_single_document(s3_key: str):
    """Analyze a single document from S3 using AI"""
    try:
        # Download file from S3 to temporary location
        temp_file_path = s3_manager.download_file_to_temp(s3_key)

        try:
            # Analyze document with AI
            result = analyze_document(temp_file_path)

            if isinstance(result, dict) and "error" not in result:
                # Save analysis result to S3
                analysis_filename = f"analysis_{Path(s3_key).stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                s3_manager.upload_json_data(
                    data=result,
                    filename=analysis_filename,
                    folder='analysis_results'
                )

                return {
                    "status": "success",
                    "analysis": result,
                    "s3_key": s3_key,
                    "analysis_saved_to": f"analysis-results/{analysis_filename}",
                    "analyzed_at": datetime.now().isoformat()
                }
            else:
                raise HTTPException(status_code=500, detail=f"Analysis failed: {result}")

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis error: {str(e)}")

@app.post("/analysis-results")
async def get_document_analysis_results(patient_s3_prefix: str):
    """Get comprehensive analysis results for all documents with S3 prefix"""
    try:
        # Get all patient documents from S3
        all_files = s3_manager.list_files(folder="uploads")
        patient_files = [f for f in all_files if patient_s3_prefix in f["s3_key"]]

        if not patient_files:
            raise HTTPException(status_code=404, detail=f"No documents found for patient: {patient_s3_prefix}")

        # Set up AI for classification
        from document_analysis_results import classify_document_with_ai
        from ai_document_analyzer import setup_groq_client

        client, model, status = setup_groq_client()
        if status != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"AI setup failed: {status}")

        # Analyze each document
        analyses = []
        categories = {
            "Cognitive Tests": [],
            "Academic Tests": [],
            "Inventories": [],
            "Behavioral Observation Scales": [],
            "Background Info": [],
            "Forms/Consent": [],
            "Communication Logs": [],
            "Other": []
        }

        for file_info in patient_files:
            try:
                # Download and read document
                temp_file_path = s3_manager.download_file_to_temp(file_info["s3_key"])

                try:
                    document_text = read_document(temp_file_path)

                    if not document_text.startswith("ERROR"):
                        # Classify with AI
                        classification, ai_status = classify_document_with_ai(
                            document_text[:3000],  # Limit text length
                            file_info["filename"],
                            client,
                            model
                        )

                        analysis = {
                            "filename": file_info["filename"],
                            "s3_key": file_info["s3_key"],
                            "classification": classification,
                            "status": ai_status
                        }

                        analyses.append(analysis)

                        # Add to category
                        if ai_status == "SUCCESS" and "classification" in analysis:
                            category = analysis["classification"].get("category", "Other")
                            if category in categories:
                                categories[category].append(analysis)
                            else:
                                categories["Other"].append(analysis)

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            except Exception as e:
                print(f"Error analyzing {file_info['filename']}: {e}")
                continue

        # Create summary
        successful_analyses = [a for a in analyses if a.get("status") == "SUCCESS"]

        # Calculate average confidence
        confidences = []
        for analysis in successful_analyses:
            if "classification" in analysis and "confidence" in analysis["classification"]:
                try:
                    confidence = int(analysis["classification"]["confidence"])
                    confidences.append(confidence)
                except (ValueError, TypeError):
                    pass

        average_confidence = round(sum(confidences) / len(confidences), 1) if confidences else 0

        result = {
            "patient_s3_prefix": patient_s3_prefix,
            "total_documents": len(patient_files),
            "analyses": analyses,
            "categories": categories,
            "summary": {
                "total_documents": len(patient_files),
                "successful_analyses": len(successful_analyses),
                "failed_analyses": len(patient_files) - len(successful_analyses),
                "categories_found": {cat: len(docs) for cat, docs in categories.items() if docs},
                "average_confidence": average_confidence
            }
        }

        return {
            "status": "success",
            "results": result,
            "analyzed_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 analysis error: {str(e)}")

@app.post("/generate-report")
async def generate_report_from_s3(patient_s3_prefix: str):
    """Generate a comprehensive psychological report directly from S3 documents"""
    try:
        print(f"🚀 Starting report generation for patient: {patient_s3_prefix}")

        # Step 1: Find all patient documents in S3
        print("📋 Step 1: Finding patient documents in S3...")
        all_files = s3_manager.list_files(folder="uploads")
        patient_files = [f for f in all_files if patient_s3_prefix in f["s3_key"]]

        if not patient_files:
            raise HTTPException(status_code=404, detail=f"No documents found for patient: {patient_s3_prefix}")

        print(f"📄 Found {len(patient_files)} documents")

        # Step 2: Analyze each document with AI
        print("🧠 Step 2: Analyzing documents with AI...")
        detailed_analyses = []
        patient_name = "Unknown_Patient"

        for i, file_info in enumerate(patient_files[:10], 1):  # Limit to 10 for performance
            print(f"   📋 Analyzing {i}/{min(len(patient_files), 10)}: {file_info['filename']}")

            try:
                # Download and analyze document
                temp_file_path = s3_manager.download_file_to_temp(file_info["s3_key"])

                try:
                    analysis_result = analyze_document(temp_file_path)

                    if isinstance(analysis_result, dict) and "error" not in analysis_result:
                        detailed_analyses.append(analysis_result)

                        # Extract patient name from first successful analysis
                        if patient_name == "Unknown_Patient" and "patient_info" in analysis_result:
                            patient_name = analysis_result["patient_info"].get("name", "Unknown_Patient")

                        print(f"      ✅ Success: {analysis_result.get('document_type', 'Unknown type')}")
                    else:
                        print(f"      ❌ Failed to analyze: {file_info['filename']}")

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            except Exception as e:
                print(f"      ❌ Error analyzing {file_info['filename']}: {e}")
                continue

        if not detailed_analyses:
            raise HTTPException(status_code=500, detail="No documents could be analyzed successfully")

        print(f"✅ Successfully analyzed {len(detailed_analyses)} documents")

        # Step 3: Generate comprehensive psychological report
        print("📝 Step 3: Generating comprehensive report with AI...")

        report_result = generate_psychological_report(detailed_analyses)

        if report_result.get("status") != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"Report generation failed: {report_result.get('error')}")

        # Step 4: Save report to S3
        print("💾 Step 4: Saving report to S3...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_name}_{timestamp}_psychological_report.txt"

        upload_result = s3_manager.upload_file(
            file_content=report_result["report"].encode('utf-8'),
            filename=filename,
            folder='reports',
            metadata={
                'patient-name': patient_name,
                'patient-s3-prefix': patient_s3_prefix,
                'report-type': 'psychological-assessment',
                'documents-analyzed': str(len(detailed_analyses)),
                'generated-by': 'direct-s3-workflow'
            }
        )

        # Step 5: Save analysis results to S3 for future reference
        print("📊 Step 5: Saving analysis results to S3...")

        analysis_filename = f"{safe_name}_{timestamp}_analysis_data.json"
        analysis_upload = s3_manager.upload_json_data(
            data={
                "patient_name": patient_name,
                "patient_s3_prefix": patient_s3_prefix,
                "timestamp": datetime.now().isoformat(),
                "documents_analyzed": len(detailed_analyses),
                "total_documents": len(patient_files),
                "detailed_analyses": detailed_analyses,
                "report_s3_key": upload_result["s3_key"]
            },
            filename=analysis_filename,
            folder='analysis_results'
        )

        print("✅ Report generation completed successfully!")

        return {
            "status": "success",
            "patient_name": patient_name,
            "patient_s3_prefix": patient_s3_prefix,
            "documents_found": len(patient_files),
            "documents_analyzed": len(detailed_analyses),
            "report": report_result["report"],
            "report_s3_key": upload_result["s3_key"],
            "report_s3_url": upload_result["s3_url"],
            "report_filename": filename,
            "analysis_s3_key": analysis_upload["s3_key"],
            "analysis_s3_url": analysis_upload["s3_url"],
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 report generation error: {str(e)}")

@app.post("/generate-cheatsheet")
async def generate_cheatsheet_from_s3():
    """Generate cheatsheet templates directly from S3 reports using existing AI"""
    try:
        print("🚀 Starting cheatsheet generation from S3 reports...")

        # Step 1: Get all reports from S3
        print("📋 Step 1: Finding reports in S3...")
        report_files = s3_manager.list_files(folder="reports")

        if not report_files:
            raise HTTPException(status_code=404, detail="No reports found in S3")

        print(f"📄 Found {len(report_files)} reports")

        # Step 2: Download and read reports
        print("📖 Step 2: Reading report contents...")
        report_contents = []

        for i, report_file in enumerate(report_files[:10], 1):  # Limit to 10 for performance
            if "_psychological_report" in report_file["filename"] or "_report" in report_file["filename"]:
                print(f"   📋 Reading {i}: {report_file['filename']}")

                try:
                    # Download report content
                    content_bytes = s3_manager.download_file(report_file["s3_key"])
                    content = content_bytes.decode('utf-8')

                    if len(content.strip()) > 100:  # Only include substantial reports
                        report_contents.append({
                            "filename": report_file["filename"],
                            "content": content,
                            "s3_key": report_file["s3_key"]
                        })
                        print(f"      ✅ Added ({len(content)} chars)")
                    else:
                        print(f"      ⏭️  Skipped (too short)")

                except Exception as e:
                    print(f"      ❌ Error reading {report_file['filename']}: {e}")
                    continue

        if not report_contents:
            raise HTTPException(status_code=404, detail="No readable reports found")

        print(f"✅ Successfully read {len(report_contents)} reports")

        # Step 3: Set up AI for template extraction
        print("🧠 Step 3: Setting up AI for template extraction...")
        from ai_document_analyzer import setup_groq_client

        client, model, status = setup_groq_client()
        if status != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"AI setup failed: {status}")

        print(f"✅ AI ready (Model: {model})")

        # Step 4: Extract templates using AI
        print("🎯 Step 4: Extracting templates with AI...")

        # Create a comprehensive prompt for template extraction
        all_reports_text = "\n\n--- REPORT SEPARATOR ---\n\n".join([r["content"] for r in report_contents])

        template_prompt = f"""
You are an expert clinical psychologist analyzing multiple psychological reports to create reusable templates.

REPORTS TO ANALYZE:
{all_reports_text[:15000]}

Create a comprehensive cheatsheet with reusable templates. Extract common patterns and create templates with placeholders.

Return a JSON response with these categories:

{{
    "background_templates": [
        "{{Patient_Name}}, a {{Age}} y/o {{Gender}}, was referred for...",
        "{{Patient_Name}} presented with {{Primary_Concern}} affecting {{Area_of_Impact}}..."
    ],
    "assessment_templates": [
        "Cognitive assessment reveals {{Test_Name}} FSIQ of {{Score}} ({{Percentile}})",
        "Behavioral assessment indicates {{Level}} {{Concern}} ({{Scale}}: {{Score}})"
    ],
    "findings_templates": [
        "Results indicate {{Level}} {{Ability}} in {{Domain}}",
        "{{Patient_Name}} demonstrates {{Strength}} in {{Area}} with concerns in {{Weakness_Area}}"
    ],
    "recommendation_templates": [
        "Recommend {{Frequency}} {{Treatment_Type}} to address {{Target_Issue}}",
        "Educational accommodations including {{Accommodation_Type}} for {{Subject_Area}}"
    ],
    "score_templates": [
        "{{Test_Name}}: {{Subtest}} {{Score}} ({{Percentile}}) - {{Interpretation}}",
        "Standard Score: {{Score}} ({{Range}}) indicating {{Level}} performance"
    ],
    "common_phrases": [
        "clinically significant",
        "within normal limits",
        "at-risk levels"
    ]
}}

Focus on creating practical, reusable templates that psychologists can use. Use clear placeholders like {{Patient_Name}}, {{Score}}, {{Test_Name}}, etc.

Return ONLY valid JSON, no other text.
"""

        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": template_prompt}
                ],
                temperature=0.1,  # Low temperature for consistent results
                max_tokens=2000
            )

            ai_response = response.choices[0].message.content

            # Try to parse the JSON response
            try:
                cheatsheet_data = json.loads(ai_response)
                print("✅ AI successfully extracted templates")
            except json.JSONDecodeError:
                # Try to extract JSON from response
                import re
                json_match = re.search(r'(\{.*\})', ai_response, re.DOTALL)
                if json_match:
                    cheatsheet_data = json.loads(json_match.group(1))
                    print("✅ AI templates extracted (with cleanup)")
                else:
                    raise Exception("Could not parse AI response as JSON")

        except Exception as e:
            print(f"❌ AI template extraction failed: {e}")
            # Fallback: Create basic templates
            cheatsheet_data = {
                "background_templates": [
                    "{Patient_Name}, a {Age} y/o {Gender}, was referred for psychological assessment.",
                    "{Patient_Name} presented with {Primary_Concern} affecting daily functioning."
                ],
                "assessment_templates": [
                    "Cognitive assessment reveals {Test_Name} FSIQ of {Score} ({Percentile}).",
                    "Behavioral assessment indicates {Level} {Concern} ({Scale}: {Score})."
                ],
                "findings_templates": [
                    "Results indicate {Level} {Ability} in {Domain}.",
                    "{Patient_Name} demonstrates strengths in {Area} with concerns in {Weakness_Area}."
                ],
                "recommendation_templates": [
                    "Recommend {Frequency} {Treatment_Type} to address {Target_Issue}.",
                    "Educational accommodations including {Accommodation_Type} for {Subject_Area}."
                ],
                "score_templates": [
                    "{Test_Name}: {Subtest} {Score} ({Percentile}) - {Interpretation}",
                    "Standard Score: {Score} ({Range}) indicating {Level} performance"
                ],
                "common_phrases": [
                    "clinically significant", "within normal limits", "at-risk levels"
                ]
            }
            print("✅ Using fallback templates")

        # Step 5: Add metadata and save to S3
        print("💾 Step 5: Saving cheatsheet to S3...")

        # Add metadata to cheatsheet
        final_cheatsheet = {
            "generated_at": datetime.now().isoformat(),
            "reports_analyzed": len(report_contents),
            "source_reports": [r["filename"] for r in report_contents],
            "ai_model": model,
            "templates": cheatsheet_data
        }

        # Save to S3
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ai_cheatsheet_{timestamp}.json"

        upload_result = s3_manager.upload_json_data(
            data=final_cheatsheet,
            filename=filename,
            folder='cheatsheets'
        )

        print("✅ Cheatsheet generation completed successfully!")

        return {
            "status": "success",
            "cheatsheet": final_cheatsheet,
            "reports_analyzed": len(report_contents),
            "total_templates": sum(len(templates) if isinstance(templates, list) else 0
                                 for templates in cheatsheet_data.values()),
            "s3_key": upload_result["s3_key"],
            "s3_url": upload_result["s3_url"],
            "filename": filename,
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 cheatsheet generation error: {str(e)}")

@app.post("/search-similar")
async def search_similar_reports_api(target_s3_key: str, filters: Optional[Dict[str, Any]] = None):
    """Search for similar reports in S3 using AI-powered matching"""
    try:
        # Download target report from S3
        target_content_bytes = s3_manager.download_file(target_s3_key)
        target_content = target_content_bytes.decode('utf-8')

        # Get all reports from S3
        report_files = s3_manager.list_files(folder="reports")

        if not report_files:
            raise HTTPException(status_code=404, detail="No reports found in S3")

        # Set up AI
        from search_similar_reports import calculate_similarity_with_ai, extract_report_metadata
        from ai_document_analyzer import setup_groq_client

        client, model, status = setup_groq_client()
        if status != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"AI setup failed: {status}")

        # Extract target metadata
        target_metadata = extract_report_metadata(target_content, Path(target_s3_key).name)

        # Calculate similarities
        similarities = []
        for report_file in report_files[:10]:  # Limit to 10 for performance
            if report_file["s3_key"] == target_s3_key:
                continue  # Skip target file

            if "_psychological_report" in report_file["filename"]:
                try:
                    # Download comparison report
                    comparison_bytes = s3_manager.download_file(report_file["s3_key"])
                    comparison_content = comparison_bytes.decode('utf-8')

                    # Extract metadata
                    comparison_metadata = extract_report_metadata(comparison_content, report_file["filename"])

                    # Apply filters if provided
                    if filters:
                        # Simple filter implementation
                        if filters.get("age_min") and comparison_metadata.get("age"):
                            if comparison_metadata["age"] < filters["age_min"]:
                                continue
                        if filters.get("age_max") and comparison_metadata.get("age"):
                            if comparison_metadata["age"] > filters["age_max"]:
                                continue
                        if filters.get("gender") and filters["gender"] != "All":
                            if comparison_metadata.get("gender") != filters["gender"]:
                                continue

                    # Calculate similarity with AI
                    similarity, ai_status = calculate_similarity_with_ai(
                        target_content[:2000],  # Limit length
                        comparison_content[:2000],
                        client,
                        model
                    )

                    if ai_status == "SUCCESS":
                        similarity["s3_key"] = report_file["s3_key"]
                        similarity["filename"] = report_file["filename"]
                        similarity["metadata"] = comparison_metadata
                        similarities.append(similarity)

                except Exception as e:
                    print(f"Error processing {report_file['filename']}: {e}")
                    continue

        # Sort by similarity
        similarities.sort(key=lambda x: int(x.get("overall_similarity", 0)), reverse=True)

        result = {
            "target_s3_key": target_s3_key,
            "target_metadata": target_metadata,
            "similar_reports": similarities,
            "total_compared": len(similarities),
            "status": "SUCCESS"
        }

        return {
            "status": "success",
            "search_results": result,
            "searched_at": datetime.now().isoformat()
        }

    except Exception as e:
        if "NoSuchKey" in str(e):
            raise HTTPException(status_code=404, detail="Target report not found in S3")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")

@app.post("/complete-workflow")
async def run_complete_workflow(patient_s3_prefix: str):
    """Run the complete psychometrist portal workflow using S3"""
    try:
        print(f"🚀 Starting S3-based complete workflow for: {patient_s3_prefix}")

        # Step 1: Get all patient documents from S3
        print("📋 Step 1: Finding patient documents in S3...")
        all_files = s3_manager.list_files(folder="uploads")
        patient_files = [f for f in all_files if patient_s3_prefix in f["s3_key"]]

        if not patient_files:
            raise HTTPException(status_code=404, detail=f"No documents found for patient: {patient_s3_prefix}")

        print(f"📄 Found {len(patient_files)} documents")

        # Step 2: Analyze each document with AI
        print("🧠 Step 2: Analyzing documents with AI...")
        detailed_analyses = []
        patient_name = "Unknown_Patient"

        for i, file_info in enumerate(patient_files[:10], 1):  # Limit to 10 for performance
            print(f"   📋 Analyzing {i}/{min(len(patient_files), 10)}: {file_info['filename']}")

            try:
                # Download and analyze document
                temp_file_path = s3_manager.download_file_to_temp(file_info["s3_key"])

                try:
                    from ai_document_analyzer import analyze_document
                    analysis_result = analyze_document(temp_file_path)

                    if isinstance(analysis_result, dict) and "error" not in analysis_result:
                        detailed_analyses.append(analysis_result)

                        # Extract patient name from first successful analysis
                        if patient_name == "Unknown_Patient" and "patient_info" in analysis_result:
                            patient_name = analysis_result["patient_info"].get("name", "Unknown_Patient")

                        print(f"      ✅ Success: {analysis_result.get('document_type', 'Unknown type')}")
                    else:
                        print(f"      ❌ Failed to analyze: {file_info['filename']}")

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            except Exception as e:
                print(f"      ❌ Error analyzing {file_info['filename']}: {e}")
                continue

        if not detailed_analyses:
            raise HTTPException(status_code=500, detail="No documents could be analyzed successfully")

        print(f"✅ Successfully analyzed {len(detailed_analyses)} documents")

        # Step 3: Generate comprehensive psychological report
        print("📝 Step 3: Generating comprehensive report...")

        report_result = generate_psychological_report(detailed_analyses)

        if report_result.get("status") != "SUCCESS":
            raise HTTPException(status_code=500, detail=f"Report generation failed: {report_result.get('error')}")

        # Step 4: Save report to S3
        print("💾 Step 4: Saving report to S3...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        report_filename = f"{safe_name}_{timestamp}_comprehensive_report.txt"

        report_upload = s3_manager.upload_file(
            file_content=report_result["report"].encode('utf-8'),
            filename=report_filename,
            folder='reports',
            metadata={
                'patient-name': patient_name,
                'report-type': 'comprehensive-assessment',
                'documents-analyzed': str(len(detailed_analyses)),
                'generated-by': 'complete-workflow'
            }
        )

        # Step 5: Save analysis results to S3
        print("📊 Step 5: Saving analysis results to S3...")

        analysis_filename = f"{safe_name}_{timestamp}_analysis_results.json"
        analysis_upload = s3_manager.upload_json_data(
            data={
                "patient_name": patient_name,
                "patient_s3_prefix": patient_s3_prefix,
                "timestamp": datetime.now().isoformat(),
                "documents_analyzed": len(detailed_analyses),
                "total_documents": len(patient_files),
                "detailed_analyses": detailed_analyses,
                "workflow_status": "SUCCESS"
            },
            filename=analysis_filename,
            folder='analysis_results'
        )

        # Create workflow summary
        workflow_summary = {
            "patient_name": patient_name,
            "patient_s3_prefix": patient_s3_prefix,
            "timestamp": datetime.now().isoformat(),
            "documents_found": len(patient_files),
            "documents_analyzed": len(detailed_analyses),
            "report_s3_key": report_upload["s3_key"],
            "report_s3_url": report_upload["s3_url"],
            "analysis_s3_key": analysis_upload["s3_key"],
            "analysis_s3_url": analysis_upload["s3_url"],
            "status": "SUCCESS"
        }

        print("✅ Complete workflow finished successfully!")

        return {
            "status": "success",
            "workflow_result": workflow_summary,
            "completed_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 workflow error: {str(e)}")

@app.get("/reports")
async def list_reports():
    """List all generated reports from S3"""
    try:
        files = s3_manager.list_files(folder="reports")

        reports = []
        for file_info in files:
            if "_psychological_report" in file_info["filename"]:
                reports.append({
                    "filename": file_info["filename"],
                    "s3_key": file_info["s3_key"],
                    "size": file_info["size"],
                    "last_modified": file_info["last_modified"],
                    "metadata": file_info.get("metadata", {})
                })

        return {
            "status": "success",
            "reports": sorted(reports, key=lambda x: x["last_modified"], reverse=True),
            "total_reports": len(reports)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing S3 reports: {str(e)}")

@app.get("/reports/{s3_key:path}")
async def get_report_content(s3_key: str):
    """Get the content of a specific report from S3"""
    try:
        # Download report content from S3
        content_bytes = s3_manager.download_file(s3_key)
        content = content_bytes.decode('utf-8')

        # Get metadata
        metadata = s3_manager.get_file_metadata(s3_key)

        return {
            "status": "success",
            "s3_key": s3_key,
            "filename": Path(s3_key).name,
            "content": content,
            "size": metadata["size"],
            "last_modified": metadata["last_modified"],
            "retrieved_at": datetime.now().isoformat()
        }

    except Exception as e:
        if "NoSuchKey" in str(e):
            raise HTTPException(status_code=404, detail="Report not found in S3")
        raise HTTPException(status_code=500, detail=f"Error retrieving S3 report: {str(e)}")

@app.delete("/reports/{s3_key:path}")
async def delete_report(s3_key: str):
    """Delete a specific report from S3"""
    try:
        success = s3_manager.delete_file(s3_key)

        if success:
            return {
                "status": "success",
                "message": f"Report {Path(s3_key).name} deleted successfully from S3",
                "s3_key": s3_key,
                "deleted_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Delete operation failed")

    except Exception as e:
        if "NoSuchKey" in str(e):
            raise HTTPException(status_code=404, detail="Report not found in S3")
        raise HTTPException(status_code=500, detail=f"Error deleting S3 report: {str(e)}")

@app.get("/s3/files")
async def list_s3_files(folder: str = "uploads"):
    """List files in S3 folder"""
    try:
        files = s3_manager.list_files(folder=folder)
        return {
            "status": "success",
            "folder": folder,
            "files": files,
            "total_files": len(files)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing S3 files: {str(e)}")

@app.get("/s3/download/{s3_key:path}")
async def get_s3_file_url(s3_key: str, expiration: int = 3600):
    """Generate presigned URL for S3 file download"""
    try:
        url = s3_manager.generate_presigned_url(s3_key, expiration=expiration)
        return {
            "status": "success",
            "s3_key": s3_key,
            "download_url": url,
            "expires_in": expiration,
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating download URL: {str(e)}")

@app.delete("/s3/files/{s3_key:path}")
async def delete_s3_file(s3_key: str):
    """Delete file from S3"""
    try:
        success = s3_manager.delete_file(s3_key)
        if success:
            return {
                "status": "success",
                "message": f"File {s3_key} deleted successfully",
                "deleted_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Delete operation failed")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting S3 file: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check with system status"""
    try:
        # Check if AI system is working
        from ai_document_analyzer import setup_groq_client
        _, model, ai_status = setup_groq_client()

        # Check S3 connection
        s3_health = s3_manager.health_check()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "ai_system": {
                "groq_status": ai_status,
                "model": model if ai_status == "SUCCESS" else None
            },
            "s3_system": s3_health,
            "version": "1.0.0"
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=3000,
        reload=True,  # Enable auto-reload
        reload_dirs=["./"],  # Watch all files in current directory
        workers=1,
        log_config=None
    )