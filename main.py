#!/usr/bin/env python3
"""
FastAPI Backend for Psychometrist Portal
Provides REST APIs for all AI-powered psychological assessment features
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import json
import shutil
from pathlib import Path
from datetime import datetime
import uuid

# Import our AI systems
from document_reader import read_document
from ai_document_analyzer import analyze_document
from document_analysis_results import analyze_patient_documents
from ai_report_generator import generate_psychological_report
from ai_cheatsheet_generator import analyze_reports_for_cheatsheet
from search_similar_reports import search_similar_reports
from complete_workflow import process_patient_complete_workflow
from s3_manager import s3_manager

# Initialize FastAPI app
app = FastAPI(
    title="Deanna's Psychometrist Portal API",
    description="AI-powered psychological assessment and report generation system",
    version="1.0.0"
)

# Add CORS middleware for web frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories for uploads and outputs
UPLOAD_DIR = Path("uploads")
REPORTS_DIR = Path("reports")
TEMP_DIR = Path("temp")

for directory in [UPLOAD_DIR, REPORTS_DIR, TEMP_DIR]:
    directory.mkdir(exist_ok=True)

# Pydantic models for request/response
class DocumentAnalysisResponse(BaseModel):
    document_type: str
    patient_info: Dict[str, Any]
    assessment_details: Dict[str, Any]
    key_scores: List[str]
    main_findings: List[str]
    recommendations: List[str]
    confidence_level: str

class ReportGenerationRequest(BaseModel):
    patient_analyses: List[Dict[str, Any]]

class CheatsheetRequest(BaseModel):
    reports_directory: Optional[str] = "reports"

class SimilarReportsRequest(BaseModel):
    target_report_path: str
    filters: Optional[Dict[str, Any]] = None

class WorkflowRequest(BaseModel):
    patient_folder_path: str

# API Endpoints

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a document file to S3 for analysis"""
    try:
        # Read file content
        file_content = await file.read()

        # Upload to S3
        upload_result = s3_manager.upload_file(
            file_content=file_content,
            filename=file.filename,
            folder='uploads',
            metadata={
                'content-type': file.content_type or 'application/octet-stream',
                'uploaded-by': 'api-user'  # In production, get from auth
            }
        )

        return {
            "file_id": upload_result["file_id"],
            "filename": upload_result["filename"],
            "s3_key": upload_result["s3_key"],
            "s3_url": upload_result["s3_url"],
            "size": upload_result["size"],
            "upload_time": upload_result["upload_time"],
            "status": "uploaded_to_s3"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"S3 upload failed: {str(e)}")

@app.post("/analyze-document")
async def analyze_single_document(s3_key: str):
    """Analyze a single document from S3 using AI"""
    try:
        # Download file from S3 to temporary location
        temp_file_path = s3_manager.download_file_to_temp(s3_key)

        try:
            # Analyze document with AI
            result = analyze_document(temp_file_path)

            if isinstance(result, dict) and "error" not in result:
                # Save analysis result to S3
                analysis_filename = f"analysis_{Path(s3_key).stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                s3_manager.upload_json_data(
                    data=result,
                    filename=analysis_filename,
                    folder='analysis_results'
                )

                return {
                    "status": "success",
                    "analysis": result,
                    "s3_key": s3_key,
                    "analysis_saved_to": f"analysis-results/{analysis_filename}",
                    "analyzed_at": datetime.now().isoformat()
                }
            else:
                raise HTTPException(status_code=500, detail=f"Analysis failed: {result}")

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis error: {str(e)}")

@app.post("/analysis-results")
async def get_document_analysis_results(request: WorkflowRequest):
    """Get comprehensive analysis results for all documents in a patient folder"""
    try:
        result = analyze_patient_documents(request.patient_folder_path)

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        return {
            "status": "success",
            "results": result,
            "analyzed_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis error: {str(e)}")

@app.post("/generate-report")
async def generate_ai_report(request: ReportGenerationRequest):
    """Generate a comprehensive psychological report using AI"""
    try:
        result = generate_psychological_report(request.patient_analyses)

        if result.get("status") == "SUCCESS":
            return {
                "status": "success",
                "report": result["report"],
                "generated_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "Report generation failed"))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Report generation error: {str(e)}")

@app.post("/generate-cheatsheet")
async def generate_ai_cheatsheet(request: CheatsheetRequest):
    """Generate AI-powered cheatsheet templates from existing reports"""
    try:
        result = analyze_reports_for_cheatsheet(request.reports_directory)

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        return {
            "status": "success",
            "cheatsheet": result["cheatsheet"],
            "reports_analyzed": result["reports_analyzed"],
            "output_file": result["output_file"],
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cheatsheet generation error: {str(e)}")

@app.post("/search-similar")
async def search_similar_reports_api(request: SimilarReportsRequest):
    """Search for similar reports using AI-powered matching"""
    try:
        result = search_similar_reports(
            request.target_report_path,
            filters=request.filters
        )

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        return {
            "status": "success",
            "search_results": result,
            "searched_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")

@app.post("/complete-workflow")
async def run_complete_workflow(request: WorkflowRequest):
    """Run the complete psychometrist portal workflow"""
    try:
        result = process_patient_complete_workflow(request.patient_folder_path)

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        return {
            "status": "success",
            "workflow_result": result,
            "completed_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Workflow error: {str(e)}")

@app.get("/reports")
async def list_reports():
    """List all generated reports"""
    try:
        reports = []
        for report_file in REPORTS_DIR.glob("*_psychological_report.txt"):
            stat = report_file.stat()
            reports.append({
                "filename": report_file.name,
                "path": str(report_file),
                "size": stat.st_size,
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
            })

        return {
            "status": "success",
            "reports": sorted(reports, key=lambda x: x["modified"], reverse=True),
            "total_reports": len(reports)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing reports: {str(e)}")

@app.get("/reports/{filename}")
async def get_report_content(filename: str):
    """Get the content of a specific report"""
    try:
        report_path = REPORTS_DIR / filename

        if not report_path.exists():
            raise HTTPException(status_code=404, detail="Report not found")

        with open(report_path, 'r', encoding='utf-8') as f:
            content = f.read()

        return {
            "status": "success",
            "filename": filename,
            "content": content,
            "retrieved_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving report: {str(e)}")

@app.delete("/reports/{filename}")
async def delete_report(filename: str):
    """Delete a specific report"""
    try:
        report_path = REPORTS_DIR / filename

        if not report_path.exists():
            raise HTTPException(status_code=404, detail="Report not found")

        report_path.unlink()

        return {
            "status": "success",
            "message": f"Report {filename} deleted successfully",
            "deleted_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting report: {str(e)}")

@app.get("/s3/files")
async def list_s3_files(folder: str = "uploads"):
    """List files in S3 folder"""
    try:
        files = s3_manager.list_files(folder=folder)
        return {
            "status": "success",
            "folder": folder,
            "files": files,
            "total_files": len(files)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing S3 files: {str(e)}")

@app.get("/s3/download/{s3_key:path}")
async def get_s3_file_url(s3_key: str, expiration: int = 3600):
    """Generate presigned URL for S3 file download"""
    try:
        url = s3_manager.generate_presigned_url(s3_key, expiration=expiration)
        return {
            "status": "success",
            "s3_key": s3_key,
            "download_url": url,
            "expires_in": expiration,
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating download URL: {str(e)}")

@app.delete("/s3/files/{s3_key:path}")
async def delete_s3_file(s3_key: str):
    """Delete file from S3"""
    try:
        success = s3_manager.delete_file(s3_key)
        if success:
            return {
                "status": "success",
                "message": f"File {s3_key} deleted successfully",
                "deleted_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Delete operation failed")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting S3 file: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check with system status"""
    try:
        # Check if AI system is working
        from ai_document_analyzer import setup_groq_client
        _, model, ai_status = setup_groq_client()

        # Check S3 connection
        s3_health = s3_manager.health_check()

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "ai_system": {
                "groq_status": ai_status,
                "model": model if ai_status == "SUCCESS" else None
            },
            "s3_system": s3_health,
            "version": "1.0.0"
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # Enable auto-reload
        reload_dirs=["./"],  # Watch all files in current directory
        workers=1
    )