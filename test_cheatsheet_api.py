#!/usr/bin/env python3
"""
Test the updated generate-cheatsheet API
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_generate_cheatsheet():
    """Test the new cheatsheet generation API"""
    print("🚀 Testing Generate Cheatsheet API")
    print("=" * 50)
    
    # Test the API
    response = requests.post(f"{BASE_URL}/generate-cheatsheet")
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Cheatsheet generated successfully!")
        print(f"📊 Reports analyzed: {data['reports_analyzed']}")
        print(f"📝 Total templates: {data['total_templates']}")
        print(f"☁️  S3 Key: {data['s3_key']}")
        print(f"📄 Filename: {data['filename']}")
        
        # Show some templates
        cheatsheet = data['cheatsheet']
        templates = cheatsheet.get('templates', {})
        
        print("\n📋 Sample Templates:")
        for category, template_list in templates.items():
            if isinstance(template_list, list) and template_list:
                print(f"\n{category.replace('_', ' ').title()}:")
                for i, template in enumerate(template_list[:2], 1):  # Show first 2
                    print(f"  {i}. {template}")
        
        return True
    else:
        print(f"❌ Cheatsheet generation failed: {response.status_code}")
        print(f"Error: {response.text}")
        return False

def test_health_check():
    """Test API health"""
    print("🔍 Testing API Health...")
    response = requests.get(f"{BASE_URL}/health")
    
    if response.status_code == 200:
        health = response.json()
        print(f"✅ API Status: {health['status']}")
        print(f"🤖 AI System: {health['ai_system']['groq_status']}")
        print(f"☁️  S3 System: {health['s3_system']['status']}")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def main():
    """Run the test"""
    print("🧪 TESTING UPDATED CHEATSHEET API")
    print("=" * 60)
    
    # Check health first
    if not test_health_check():
        print("❌ API not healthy. Stopping test.")
        return
    
    print()
    
    # Test cheatsheet generation
    success = test_generate_cheatsheet()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 CHEATSHEET API TEST COMPLETED SUCCESSFULLY!")
        print("\n📋 Summary:")
        print("✅ Direct S3 integration working")
        print("✅ AI template extraction working")
        print("✅ Fallback templates available")
        print("✅ Results saved to S3")
    else:
        print("❌ CHEATSHEET API TEST FAILED!")
    
    print(f"\n🌐 API Documentation: {BASE_URL}/docs")

if __name__ == "__main__":
    main()
